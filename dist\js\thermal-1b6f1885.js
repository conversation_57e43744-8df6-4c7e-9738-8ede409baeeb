import{g as t}from"./i18n-6ade161d.js";const n=new class{constructor(){this.isInitialized=!1,this.thermalPrinterDetected=!1,this.printerSettings={width:"80mm",fontSize:"14px",fontFamily:"Courier New, monospace",lineHeight:"1.4",margin:"3mm",autocut:!0,encoding:"UTF-8"},this.init()}async init(){try{await this.detectThermalPrinter(),this.isInitialized=!0,console.log("🖨️ Thermal Printer: System initialized successfully")}catch(t){console.warn("🖨️ Thermal Printer: Initialization failed, using fallback mode",t),this.isInitialized=!0}}async detectThermalPrinter(){try{const t=["thermal","receipt","pos","epson","star","citizen","bixolon","custom","zebra","datamax","tsc"];if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){const n=await navigator.mediaDevices.enumerateDevices();this.thermalPrinterDetected=n.some((n=>t.some((t=>n.label.toLowerCase().includes(t)))))}return!this.thermalPrinterDetected&&window.print&&(this.thermalPrinterDetected=!0),console.log("🖨️ Thermal Printer: Detection result - "+(this.thermalPrinterDetected?"Found":"Not found")),this.thermalPrinterDetected}catch(t){return console.warn("🖨️ Thermal Printer: Detection failed",t),this.thermalPrinterDetected=!1,!1}}printInvoice(n,e={}){const{language:i="ar",showToast:r=()=>{},formatPrice:o=t=>t.toFixed(2),directPrint:a=!0,storeSettings:l=null}=e;try{const t=this.generateInvoiceContent(n,i,o,l);a&&this.thermalPrinterDetected?this.printDirectly(t,i,r):this.printWithWindow(t,i,r)}catch(s){console.error("🖨️ Thermal Printer: Print failed",s),r(`❌ ${t("printError",i)||"خطأ في الطباعة"}`,"error",3e3)}}generateInvoiceContent(n,e,i,r=null){const o=(n,i)=>t(n,e)||i;return`\n      <!DOCTYPE html>\n      <html dir="${"ar"===e?"rtl":"ltr"}" lang="${e}">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>${o("thermalInvoice","فاتورة حرارية")}</title>\n        <style>\n          ${this.getThermalCSS(e)}\n        </style>\n      </head>\n      <body>\n        <div class="thermal-receipt">\n          ${this.generateHeader(e,o,r)}\n          ${this.generateInvoiceInfo(n,e,o)}\n          ${this.generateItemsTable(n,e,o,i)}\n          ${this.generateTotals(n,e,o,i)}\n          ${this.generateFooter(e,o)}\n        </div>\n      </body>\n      </html>\n    `}getThermalCSS(t){const n="ar"===t;return`\n      @page {\n        size: 80mm auto;\n        margin: 0;\n      }\n      \n      * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n      }\n      \n      body {\n        font-family: ${this.printerSettings.fontFamily};\n        font-size: ${this.printerSettings.fontSize};\n        font-weight: bold;\n        line-height: ${this.printerSettings.lineHeight};\n        color: #000;\n        background: #fff;\n        width: 74mm;\n        margin: 0 auto;\n        padding: ${this.printerSettings.margin};\n        direction: ${n?"rtl":"ltr"};\n        text-align: center;\n      }\n      \n      .thermal-receipt {\n        width: 100%;\n      }\n      \n      .thermal-header {\n        font-size: 18px;\n        font-weight: bold;\n        text-transform: uppercase;\n        margin-bottom: 4mm;\n        border-bottom: 2px solid #000;\n        padding-bottom: 2mm;\n        text-align: center;\n      }\n\n      .thermal-logo {\n        text-align: center;\n        margin-bottom: 3mm;\n      }\n\n      .thermal-logo img {\n        max-width: 60mm;\n        max-height: 20mm;\n        width: auto;\n        height: auto;\n      }\n\n      .thermal-logo-fallback {\n        font-size: 28px;\n        font-weight: bold;\n        padding: 2mm;\n        display: inline-block;\n      }\n\n      .thermal-store-info {\n        text-align: center;\n        margin: 2mm 0 4mm 0;\n        line-height: 1.6;\n      }\n\n      .thermal-store-name {\n        font-size: 16px;\n        font-weight: bold;\n        margin: 1mm 0;\n      }\n\n      .thermal-store-phone {\n        font-size: 12px;\n        margin: 1mm 0;\n      }\n\n      .thermal-store-address {\n        font-size: 11px;\n        margin: 1mm 0;\n        word-wrap: break-word;\n      }\n\n      .thermal-invoice-title {\n        font-size: 18px;\n        font-weight: bold;\n        text-align: center;\n        margin: 3mm 0 1mm 0;\n        text-transform: uppercase;\n      }\n\n      .thermal-system-info {\n        font-size: 12px;\n        text-align: center;\n        margin-bottom: 3mm;\n      }\n      \n      .thermal-info {\n        font-size: 12px;\n        margin: 2mm 0;\n        text-align: ${n?"right":"left"};\n      }\n      \n      .thermal-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin: 3mm 0;\n        font-size: 10px;\n        table-layout: fixed;\n      }\n\n      .thermal-table th,\n      .thermal-table td {\n        padding: 0.5mm 1mm;\n        border-bottom: 1px solid #000;\n        overflow: hidden;\n        vertical-align: top;\n      }\n\n      .thermal-table th {\n        background: #000;\n        color: #fff;\n        font-weight: bold;\n        font-size: 9px;\n        text-align: center;\n      }\n\n      /* Responsive column widths for thermal printing */\n      .thermal-table .col-product {\n        width: 42%;\n        text-align: ${n?"right":"left"};\n        white-space: normal;\n        word-wrap: break-word;\n        line-height: 1.2;\n      }\n\n      .thermal-table .col-qty {\n        width: 12%;\n        text-align: center;\n        font-weight: bold;\n        font-size: 9px;\n      }\n\n      .thermal-table .col-price {\n        width: 23%;\n        text-align: ${n?"left":"right"};\n        font-size: 9px;\n      }\n\n      .thermal-table .col-total {\n        width: 23%;\n        text-align: ${n?"left":"right"};\n        font-weight: bold;\n        font-size: 10px;\n      }\n\n      /* Responsive adjustments for very small thermal printers */\n      @media print and (max-width: 58mm) {\n        .thermal-table {\n          font-size: 8px;\n        }\n\n        .thermal-table .col-product {\n          width: 45%;\n        }\n\n        .thermal-table .col-qty {\n          width: 10%;\n        }\n\n        .thermal-table .col-price,\n        .thermal-table .col-total {\n          width: 22.5%;\n        }\n      }\n\n      /* Responsive adjustments for larger thermal printers */\n      @media print and (min-width: 80mm) {\n        .thermal-table {\n          font-size: 11px;\n        }\n\n        .thermal-table .col-product {\n          width: 40%;\n        }\n\n        .thermal-table .col-qty {\n          width: 15%;\n        }\n\n        .thermal-table .col-price,\n        .thermal-table .col-total {\n          width: 22.5%;\n        }\n      }\n      \n      .thermal-total {\n        font-size: 16px;\n        font-weight: bold;\n        border: 3px double #000;\n        padding: 3mm;\n        margin: 3mm 0;\n        background: #f0f0f0;\n      }\n      \n      .thermal-footer {\n        font-size: 10px;\n        margin-top: 4mm;\n        border-top: 2px solid #000;\n        padding-top: 3mm;\n        text-align: center;\n      }\n      \n      .center { text-align: center; }\n      .right { text-align: right; }\n      .left { text-align: left; }\n      .bold { font-weight: bold; }\n      .separator { \n        border-top: 1px dashed #000; \n        margin: 2mm 0; \n        height: 1px; \n      }\n      \n      @media print {\n        body { \n          -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n        }\n        .no-print { display: none !important; }\n      }\n    `}generateHeader(t,n,e=null){const i=e||{storeName:"iCalDZ Store",storeNumber:"ST001",storeLogo:"",storePhone:"+213 555 123 456",storeAddress:"الجزائر العاصمة، الجزائر"};return`\n      <div class="thermal-header">\n        \x3c!-- Logo section --\x3e\n        <div class="thermal-logo">\n          ${i.storeLogo?`<img src="${i.storeLogo}" alt="${i.storeName}" />`:'<div class="thermal-logo-fallback">🏪</div>'}\n        </div>\n\n        \x3c!-- Store information section --\x3e\n        <div class="thermal-store-info">\n          <div class="thermal-store-name">\n            🏪 ${i.storeName} ${i.storeName?"*":""}\n          </div>\n          <div class="thermal-store-phone">\n            📞 ${i.storePhone||"+213 555 123 456"}\n          </div>\n          <div class="thermal-store-address">\n            📍 ${i.storeAddress||"الجزائر العاصمة، الجزائر"}\n          </div>\n        </div>\n\n        \x3c!-- Invoice title --\x3e\n        <div class="thermal-invoice-title">\n          ${n("salesInvoiceTitle","فاتورة مبيعات")}\n        </div>\n        <div class="thermal-system-info">\n          ${n("accountingSystem","نظام المحاسبي")} - iCalDZ\n        </div>\n      </div>\n    `}generateInvoiceInfo(t,n,e){const i=new Date(t.date).toLocaleDateString("ar"===n?"ar-DZ":"fr"===n?"fr-FR":"en-US"),r=new Date(t.createdAt||t.date).toLocaleTimeString("ar"===n?"ar-DZ":"fr"===n?"fr-FR":"en-US");return`\n      <div class="thermal-info">\n        <div><strong>${e("invoiceNumberLabel","فاتورة رقم:")} ${t.invoiceNumber}</strong></div>\n        <div>${e("dateLabel","التاريخ:")} ${i}</div>\n        <div>${e("timeLabel","الوقت:")} ${r}</div>\n        <div>${e("customerLabel","الزبون:")} ${t.customerName||e("walkInCustomer","زبون عابر")}</div>\n        <div>${e("paymentMethodLabel","طريقة الدفع:")} ${t.paymentMethod}</div>\n      </div>\n      <div class="separator"></div>\n    `}generateItemsTable(t,n,e,i){const r=t.items||[];let o=`\n      <table class="thermal-table">\n        <thead>\n          <tr>\n            <th class="col-product">${e("productColumn","المنتج")}</th>\n            <th class="col-qty">${e("qtyColumn","QNT")}</th>\n            <th class="col-price">${e("priceColumn","السعر")}</th>\n            <th class="col-total">${e("totalColumn","المجموع")}</th>\n          </tr>\n        </thead>\n        <tbody>\n    `;return r.forEach((t=>{let n=t.productName;if(n.length>25){const t=n.split(" ");let e="";for(const n of t){if(!((e+n).length<=22))break;e+=(e?" ":"")+n}n=e+(e.length<n.length?"...":"")}const e=i(t.price).replace(/\s+/g,""),r=i(t.total).replace(/\s+/g,"");o+=`\n        <tr>\n          <td class="col-product">${n}</td>\n          <td class="col-qty">${t.quantity}</td>\n          <td class="col-price">${e}</td>\n          <td class="col-total">${r}</td>\n        </tr>\n      `})),o+='\n        </tbody>\n      </table>\n      <div class="separator"></div>\n    ',o}generateTotals(t,n,e,i){return`\n      <div class="thermal-info">\n        <div>${e("subtotalLabel","المجموع الفرعي:")} ${i(t.subtotal||0)}</div>\n        ${t.discount>0?`<div>${e("discountLabel","الخصم:")} ${i(t.discount)}</div>`:""}\n        ${t.tax>0?`<div>${e("taxLabel","الضريبة:")} ${i(t.tax)}</div>`:""}\n      </div>\n      <div class="thermal-total">\n        ${e("finalTotalLabel","المجموع النهائي:")} ${i(t.finalTotal)}\n      </div>\n    `}generateFooter(t,n){return`\n      <div class="thermal-footer">\n        <div class="bold">${n("thankYouMessage","شكراً لزيارتكم")}</div>\n        <div style="margin-top: 2mm;">\n          ${n("developedBy","تم التطوير بواسطة")} iCode DZ\n        </div>\n        <div>📞 0551930589</div>\n        <div style="margin-top: 2mm; font-size: 8px;">\n          ${n("printedAtLabel","طُبعت في:")} ${(new Date).toLocaleString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}\n        </div>\n      </div>\n    `}printDirectly(n,e,i){try{const r=document.createElement("iframe");r.style.position="absolute",r.style.left="-9999px",r.style.top="-9999px",r.style.width="1px",r.style.height="1px",document.body.appendChild(r);const o=r.contentDocument||r.contentWindow.document;o.open(),o.write(n),o.close(),r.onload=()=>{setTimeout((()=>{try{r.contentWindow.focus(),r.contentWindow.print(),setTimeout((()=>{document.body.removeChild(r)}),1e3);i(`🖨️ ${((n,i)=>t(n,e)||i)("invoiceSentToThermalPrinter","تم إرسال الفاتورة للطباعة الحرارية")}`,"success",3e3)}catch(o){console.error("🖨️ Direct print failed, falling back to window method",o),this.printWithWindow(n,e,i)}}),500)}}catch(r){console.error("🖨️ Direct printing failed",r),this.printWithWindow(n,e,i)}}printWithWindow(n,e,i){const r=window.open("","_blank","width=400,height=700,scrollbars=yes");if(!r){return void i(`❌ ${((n,i)=>t(n,e)||i)("popupBlocked","تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة")}`,"error",5e3)}r.document.write(n),r.document.close(),r.onload=()=>{setTimeout((()=>{r.focus(),r.print(),setTimeout((()=>{r.close()}),2e3)}),1e3)};var o,a;i(`🖨️ ${o="printWindowOpened",a="تم فتح نافذة الطباعة",t(o,e)||a}`,"info",3e3)}isThermalPrinterAvailable(){return this.isInitialized&&this.thermalPrinterDetected}getStatus(){return{initialized:this.isInitialized,thermalDetected:this.thermalPrinterDetected,settings:this.printerSettings}}};export{n as t};
